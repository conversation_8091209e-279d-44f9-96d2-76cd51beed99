/** @type {import('next').NextConfig} */
const { version } = require("./package.json");

const nextConfig = {
  poweredByHeader: false, // Hide x-powered-by header
  images: {
    domains: [process.env.NEXT_PUBLIC_S3_DOMAIN],
    // Add image optimization settings to prevent preload issues
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  env: {
    VERSION: version,
  },
  // Optimize CSS loading to reduce preload warnings
  experimental: {
    optimizeCss: false, // Disable to prevent ReactDOM.preload empty href errors
  },
  async headers() {
    return [
      {
        // Apply security headers to all routes
        source: "/(.*)",
        headers: [
          // Content Security Policy - Environment-aware CSP
          {
            key: "Content-Security-Policy",
            value: (() => {
              // Properly detect environment
              const isDev =
                process.env.NODE_ENV === "development" ||
                process.env.NEXT_PUBLIC_NODE_ENV === "dev";

              return [
                "default-src 'self'",
                // More restrictive script-src - remove 'unsafe-eval' in production
                `script-src 'self' 'unsafe-inline'${
                  isDev ? " 'unsafe-eval'" : ""
                } blob: https://www.googletagmanager.com https://www.google-analytics.com https://cmp.osano.com https://*.osano.com https://www.gstatic.com https://www.google.com https://www.recaptcha.net${
                  isDev ? " localhost:* 127.0.0.1:*" : ""
                }`,
                // More restrictive worker-src
                `worker-src 'self' blob:${
                  isDev ? " localhost:* 127.0.0.1:*" : ""
                }`,
                // Keep style-src permissive for now due to inline styles
                `style-src 'self' 'unsafe-inline' https://fonts.googleapis.com${
                  isDev ? " localhost:* 127.0.0.1:*" : ""
                }`,
                "font-src 'self' https://fonts.gstatic.com data:",
                // More restrictive img-src in production
                `img-src 'self' data: blob: https: ${
                  isDev ? "http: localhost:* 127.0.0.1:*" : ""
                }`,
                // More restrictive connect-src
                `connect-src 'self' https://www.google-analytics.com https://region1.google-analytics.com https://analytics.google.com https://stats.g.doubleclick.net https://www.googletagmanager.com https://www.google.com https://google.com https://ipapi.co https://cmp.osano.com https://*.osano.com https://*.amazonaws.com https://*.amplifyapp.com https://*.apphero.io${
                  isDev
                    ? " localhost:* 127.0.0.1:* ws://localhost:* ws://127.0.0.1:*"
                    : ""
                }`,
                "frame-src 'self' https://www.google.com https://www.recaptcha.net https://recaptcha.google.com https://www.googletagmanager.com https://td.doubleclick.net https://*.doubleclick.net https://*.apphero.io https://*.amplifyapp.com",
                "object-src 'none'",
                "base-uri 'self'",
                "form-action 'self'",
                "frame-ancestors 'self'",
                "upgrade-insecure-requests",
              ].join("; ");
            })(),
          },
          // X-Content-Type-Options
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          // X-Frame-Options - Use SAMEORIGIN for better security
          {
            key: "X-Frame-Options",
            value: "SAMEORIGIN",
          },
          // Referrer Policy
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin",
          },
          // X-XSS-Protection
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
          // Strict Transport Security
          {
            key: "Strict-Transport-Security",
            value: "max-age=31536000; includeSubDomains; preload",
          },
          // Permissions Policy
          {
            key: "Permissions-Policy",
            value: ["geolocation=()"].join(", "),
          },
          // Cross-Origin-Embedder-Policy
          {
            key: "Cross-Origin-Embedder-Policy",
            value: "unsafe-none",
          },
          // Cross-Origin-Opener-Policy
          {
            key: "Cross-Origin-Opener-Policy",
            value: "same-origin-allow-popups",
          },
          // Cross-Origin-Resource-Policy
          {
            key: "Cross-Origin-Resource-Policy",
            value: "cross-origin",
          },
          // Note: Require-SRI-For header removed as it's not widely supported
          // and can cause compatibility issues with third-party scripts
        ],
      },
    ];
  },
};

module.exports = nextConfig;
